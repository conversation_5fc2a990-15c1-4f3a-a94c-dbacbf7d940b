services:
  celery:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      args:
        CELERY_USER: developer
    image: celery/celery:dev
    environment:
      TEST_BROKER: pyamqp://rabbit:5672
      TEST_BACKEND: redis://redis
      PYTHONUNBUFFERED: 1
      PYTHONDONTWRITEBYTECODE: 1
      REDIS_HOST: redis
      WORKER_LOGLEVEL: DEBUG
      AZUREBLOCKBLOB_URL: azureblockblob://DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://azurite:10000/devstoreaccount1;
      PYTHONPATH: /home/<USER>/celery
    tty: true
    volumes:
      - ../.:/home/<USER>/celery
    depends_on:
      - rabbit
      - redis
      - dynamodb
      - azurite

  rabbit:
    image: rabbitmq:latest

  redis:
    image: redis:latest

  dynamodb:
    image: amazon/dynamodb-local:latest

  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:latest

  docs:
    image: celery/docs
    build:
      context: ..
      dockerfile: docker/docs/Dockerfile
    volumes:
        - ../docs:/docs:z
    ports:
      - "7001:7000"
    command: /start-docs
