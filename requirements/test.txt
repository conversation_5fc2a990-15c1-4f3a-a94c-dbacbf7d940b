pytest==8.3.5
pytest-celery[all]>=1.2.0,<1.3.0
pytest-rerunfailures>=14.0,<15.0; python_version >= "3.8" and python_version < "3.9"
pytest-rerunfailures>=15.0; python_version >= "3.9" and python_version < "4.0"
pytest-subtests<0.14.0; python_version < "3.9"
pytest-subtests>=0.14.1; python_version >= "3.9"
pytest-timeout==2.3.1
pytest-click==1.1.0
pytest-order==1.3.0
boto3>=1.26.143
moto>=4.1.11,<5.1.0
# typing extensions
mypy==1.14.1; platform_python_implementation=="CPython"
pre-commit>=3.5.0,<3.8.0; python_version < '3.9'
pre-commit>=4.0.1; python_version >= '3.9'
-r extras/yaml.txt
-r extras/msgpack.txt
-r extras/mongodb.txt
-r extras/gcs.txt
-r extras/pydantic.txt
