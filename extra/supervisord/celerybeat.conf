; ================================
;  celery beat supervisor example
; ================================

[program:celerybeat]
; Set full path to celery program if using virtualenv
command=celery -A myapp beat --schedule /var/lib/celery/beat.db --loglevel=INFO

; remove the -A myapp argument if you aren't using an app instance

directory=/path/to/project
user=nobody
numprocs=1
stdout_logfile=/var/log/celery/beat.log
stderr_logfile=/var/log/celery/beat.log
autostart=true
autorestart=true
startsecs=10

; Causes supervisor to send the termination signal (SIGTERM) to the whole process group.
stopasgroup=true

; if rabbitmq is supervised, set its priority higher
; so it starts first
priority=999
