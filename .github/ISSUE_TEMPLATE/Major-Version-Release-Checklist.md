---
name: Major Version Release Checklist
about: About to release a new major version? (Maintainers Only!)
title: ''
labels: ''
assignees: ''

---

Version: <!-- Insert Version Here -->
Release PR: <!-- Insert Release PR Here -->

# Description

<!-- Briefly describe the contents of the version -->

# Checklist

- [ ] Release PR drafted
- [ ] Milestone is 100% done
- [ ] Merge Freeze
- [ ] Release PR reviewed
- [ ] The main branch build passes

    [![Build Status](https://github.com/celery/celery/actions/workflows/python-package.yml/badge.svg)](https://github.com/celery/celery/actions/workflows/python-package.yml)
- [ ] Release Notes
- [ ] What's New

# Process

# Alphas

<!-- Add more as needed -->
- [ ] Alpha 1

## Betas

<!-- Add more as needed -->
- [ ] Beta 1

## Release Candidates

<!-- Add more as needed -->
- [ ] RC 1

# Release Blockers

# Potential Release Blockers
