# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Celery

on:
  push:
    branches: [ 'main']
    paths:
        - '**.py'
        - '**.txt'
        - '.github/workflows/python-package.yml'
        - '**.toml'
        - "tox.ini"
  pull_request:
    branches: [ 'main' ]
    paths:
        - '**.py'
        - '**.txt'
        - '**.toml'
        - '.github/workflows/python-package.yml'
        - "tox.ini"
  workflow_dispatch:


permissions:
  contents: read # to fetch code (actions/checkout)

jobs:
  Unit:

    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12', '3.13', 'pypy-3.10']
        os: ["blacksmith-4vcpu-ubuntu-2404", "windows-latest"]
        exclude:
            - python-version: '3.9'
              os: "windows-latest"
            - python-version: 'pypy-3.10'
              os: "windows-latest"
            - python-version: '3.10'
              os: "windows-latest"
            - python-version: '3.11'
              os: "windows-latest"
            - python-version: '3.13'
              os: "windows-latest"

    steps:
    - name: Install apt packages
      if: startsWith(matrix.os, 'blacksmith-4vcpu-ubuntu')
      run: |
        sudo apt-get update && sudo apt-get install -f libcurl4-openssl-dev libssl-dev libgnutls28-dev httping expect libmemcached-dev
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: useblacksmith/setup-python@v6
      with:
        python-version: ${{ matrix.python-version }}
        allow-prereleases: true
        cache: 'pip'
        cache-dependency-path: '**/setup.py'

    - name: Install tox
      run: python -m pip install --upgrade pip 'tox' tox-gh-actions
    - name: >
        Run tox for
        "${{ matrix.python-version }}-unit"
      timeout-minutes: 30
      run: |
        tox --verbose --verbose

    - uses: codecov/codecov-action@v5
      with:
        flags: unittests # optional
        fail_ci_if_error: true # optional (default = false)
        token: ${{ secrets.CODECOV_TOKEN }}
        verbose: true # optional (default = false)

    - name: Upload test results to Codecov
      if: ${{ !cancelled() }}
      uses: codecov/test-results-action@v1
      with:
        token: ${{ secrets.CODECOV_TOKEN }}

  Integration:
      needs:
      - Unit
      if: needs.Unit.result == 'success'
      timeout-minutes: 240

      runs-on: blacksmith-4vcpu-ubuntu-2404
      strategy:
          fail-fast: false
          matrix:
              python-version: ['3.8', '3.9', '3.10', '3.11', '3.12', '3.13']
              toxenv: ['redis', 'rabbitmq', 'rabbitmq_redis']

      services:
          redis:
              image: redis
              ports:
                  - 6379:6379
              env:
                  REDIS_HOST: localhost
                  REDIS_PORT: 6379
          rabbitmq:
              image: rabbitmq
              ports:
                  - 5672:5672
              env:
                  RABBITMQ_DEFAULT_USER: guest
                  RABBITMQ_DEFAULT_PASS: guest

      steps:
          -   name: Install apt packages
              run: |
                  sudo apt-get update && sudo apt-get install -f libcurl4-openssl-dev libssl-dev libgnutls28-dev httping expect libmemcached-dev

          -   uses: actions/checkout@v4
          -   name: Set up Python ${{ matrix.python-version }}
              uses: useblacksmith/setup-python@v6
              with:
                  python-version: ${{ matrix.python-version }}
                  allow-prereleases: true
                  cache: 'pip'
                  cache-dependency-path: '**/setup.py'
          -   name: Install tox
              run: python -m pip install --upgrade pip 'tox' tox-gh-actions
          -   name: >
                  Run tox for
                  "${{ matrix.python-version }}-integration-${{ matrix.toxenv }}"
              uses: nick-fields/retry@v3
              with:
                timeout_minutes: 60
                max_attempts: 2
                retry_wait_seconds: 0
                command: |
                  tox --verbose --verbose -e "${{ matrix.python-version }}-integration-${{ matrix.toxenv }}" -vv

  Smoke:
    needs:
    - Unit
    if: needs.Unit.result == 'success'
    runs-on: blacksmith-4vcpu-ubuntu-2404
    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12', '3.13']
        test-case: [
          'test_broker_failover.py',
          'test_worker_failover.py',
          'test_native_delayed_delivery.py',
          'test_quorum_queues.py',
          'test_hybrid_cluster.py',
          'test_revoke.py',
          'test_visitor.py',
          'test_canvas.py',
          'test_consumer.py',
          'test_control.py',
          'test_signals.py',
          'test_tasks.py',
          'test_thread_safe.py',
          'test_worker.py'
        ]

    steps:
      - name: Fetch Docker Images
        run: |
          docker pull redis:latest
          docker pull rabbitmq:latest

      - name: Install apt packages
        run: |
          sudo apt update
          sudo apt-get install -y procps  # Install procps to enable sysctl
          sudo sysctl -w vm.overcommit_memory=1

      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: useblacksmith/setup-python@v6
        with:
          python-version: ${{ matrix.python-version }}
          allow-prereleases: true
          cache: 'pip'
          cache-dependency-path: '**/setup.py'

      - name: Install tox
        run: python -m pip install --upgrade pip tox tox-gh-actions

      - name: Run tox for "${{ matrix.python-version }}-smoke-${{ matrix.test-case }}"
        uses: nick-fields/retry@v3
        with:
          timeout_minutes: 20
          max_attempts: 2
          retry_wait_seconds: 0
          command: |
            tox --verbose --verbose -e "${{ matrix.python-version }}-smoke" -- -n auto -k ${{ matrix.test-case }}
