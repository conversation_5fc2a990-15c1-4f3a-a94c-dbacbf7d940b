name: Docker

on:
    pull_request:
      branches: [ 'main']
      paths:
          - '**.py'
          - '**.txt'
          - '**.toml'
          - '/docker/**'
          - '.github/workflows/docker.yml'
          - 'docker/Dockerfile'
          - 't/smoke/workers/docker/**'
    push:
      branches: [ 'main']
      paths:
          - '**.py'
          - '**.txt'
          - '**.toml'
          - '/docker/**'
          - '.github/workflows/docker.yml'
          - 'docker/Dockerfile'
          - 't/smoke/workers/docker/**'
    workflow_dispatch:


jobs:
  docker-build:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    timeout-minutes: 60
    steps:
    - uses: actions/checkout@v4
    - name: Build Docker container
      run: make docker-build

  smoke-tests_dev:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v4
    - name: "Build smoke tests container: dev"
      run: docker build -f t/smoke/workers/docker/dev .

  smoke-tests_latest:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v4
    - name: "Build smoke tests container: latest"
      run: docker build -f t/smoke/workers/docker/pypi .

  smoke-tests_pypi:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v4
    - name: "Build smoke tests container: pypi"
      run: docker build -f t/smoke/workers/docker/pypi --build-arg CELERY_VERSION="5" .

  smoke-tests_legacy:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v4
    - name: "Build smoke tests container: legacy"
      run: docker build -f t/smoke/workers/docker/pypi --build-arg CELERY_VERSION="4" .
