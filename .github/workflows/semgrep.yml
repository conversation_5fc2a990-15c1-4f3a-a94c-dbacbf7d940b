on:
  pull_request: {}
  push:
    branches:
    - main
    - master
    paths:
    - .github/workflows/semgrep.yml
  schedule:
  # random HH:MM to avoid a load spike on GitHub Actions at 00:00
  - cron: 44 6 * * *
  workflow_dispatch:

name: Semgrep
jobs:
  semgrep:
    name: <PERSON>an
    runs-on: blacksmith-4vcpu-ubuntu-2204
    env:
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
    container:
      image: returntocorp/semgrep
    steps:
    - uses: actions/checkout@v4
    - run: semgrep ci
