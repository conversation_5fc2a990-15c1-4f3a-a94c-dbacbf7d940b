import pytest
from kombu.utils.encoding import ensure_bytes

from celery.exceptions import SecurityError
from celery.security.key import <PERSON><PERSON><PERSON>
from celery.security.utils import get_digest_algorithm

from . import CERT1, <PERSON>NCKEY1, <PERSON><PERSON><PERSON>Y2, <PERSON>EY1, <PERSON>EY2, <PERSON><PERSON><PERSON>_ECDSA, K<PERSON>YPASSWORD
from .case import SecurityCase


class test_PrivateKey(SecurityCase):

    def test_valid_private_key(self):
        PrivateKey(KEY1)
        PrivateKey(KEY2)
        PrivateKey(ENCKEY1, KEYPASSWORD)
        PrivateKey(ENCKEY2, KEYPASSWORD)

    def test_invalid_private_key(self):
        with pytest.raises((SecurityError, TypeError)):
            PrivateKey(None)
        with pytest.raises(SecurityError):
            PrivateKey('')
        with pytest.raises(SecurityError):
            PrivateKey('foo')
        with pytest.raises(SecurityError):
            PrivateKey(KEY1[:20] + KEY1[21:])
        with pytest.raises(SecurityError):
            <PERSON><PERSON><PERSON>(ENCKEY1, K<PERSON>YPASSWORD+b"wrong")
        with pytest.raises(SecurityError):
            PrivateKey(ENCKEY2, K<PERSON>YPASSWORD+b"wrong")
        with pytest.raises(SecurityError):
            PrivateKey(CERT1)
        with pytest.raises(SecurityError):
            PrivateKey(KEY_ECDSA)

    def test_sign(self):
        pkey = PrivateKey(KEY1)
        pkey.sign(ensure_bytes('test'), get_digest_algorithm())
        with pytest.raises(AttributeError):
            pkey.sign(ensure_bytes('test'), get_digest_algorithm('unknown'))

        # pkey = PrivateKey(KEY_ECDSA)
        # pkey.sign(ensure_bytes('test'), get_digest_algorithm())
