[build_sphinx]
source-dir = docs/
build-dir = docs/_build
all_files = 1

[flake8]
# classes can be lowercase, arguments and variables can be uppercase
# whenever it makes the code more readable.
max-line-length = 117
extend-ignore =
    # incompatible with black https://github.com/psf/black/issues/315#issuecomment-395457972
    E203,
    # Missing docstring in public method
    D102,
    # Missing docstring in public package
    D104,
    # Missing docstring in magic method
    D105,
    # Missing docstring in __init__
    D107,
    # First line should be in imperative mood; try rephrasing
    D401,
    # No blank lines allowed between a section header and its content
    D412,
    # ambiguous variable name '...'
    E741,
    # ambiguous class definition '...'
    E742,
per-file-ignores =
   t/*,setup.py,examples/*,docs/*,extra/*:
       # docstrings
       D,

[bdist_rpm]
requires = backports.zoneinfo>=0.2.1;python_version<'3.9'
           tzdata>=2022.7
           billiard >=4.1.0,<5.0
           kombu >= 5.3.4,<6.0.0


[metadata]
license_files = LICENSE
