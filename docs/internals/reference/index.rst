===========================
 Internal Module Reference
===========================

:Release: |version|
:Date: |today|

.. toctree::
    :maxdepth: 1

    celery.worker.components
    celery.worker.loops
    celery.worker.heartbeat
    celery.worker.control
    celery.worker.pidbox
    celery.worker.autoscale
    celery.concurrency
    celery.concurrency.solo
    celery.concurrency.prefork
    celery.concurrency.eventlet
    celery.concurrency.gevent
    celery.concurrency.thread
    celery.concurrency.base
    celery.backends
    celery.backends.base
    celery.backends.asynchronous
    celery.backends.azureblockblob
    celery.backends.rpc
    celery.backends.database
    celery.backends.cache
    celery.backends.consul
    celery.backends.couchdb
    celery.backends.mongodb
    celery.backends.elasticsearch
    celery.backends.redis
    celery.backends.cassandra
    celery.backends.couchbase
    celery.backends.arangodb
    celery.backends.dynamodb
    celery.backends.filesystem
    celery.backends.cosmosdbsql
    celery.backends.s3
    celery.backends.gcs
    celery.app.trace
    celery.app.annotations
    celery.app.routes
    celery.security.certificate
    celery.security.key
    celery.security.serialization
    celery.security.utils
    celery.events.snapshot
    celery.events.cursesmon
    celery.events.dumper
    celery.backends.database.models
    celery.backends.database.session
    celery.utils
    celery.utils.abstract
    celery.utils.collections
    celery.utils.nodenames
    celery.utils.deprecated
    celery.utils.functional
    celery.utils.graph
    celery.utils.objects
    celery.utils.term
    celery.utils.time
    celery.utils.iso8601
    celery.utils.saferepr
    celery.utils.serialization
    celery.utils.sysinfo
    celery.utils.threads
    celery.utils.timer2
    celery.utils.imports
    celery.utils.log
    celery.utils.text
    celery.utils.dispatch
    celery.utils.dispatch.signal
    celery.platforms
    celery._state
