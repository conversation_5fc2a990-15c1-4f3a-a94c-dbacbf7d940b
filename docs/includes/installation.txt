.. _celery-installation:

Installation
============

You can install Celery either via the Python Package Index (PyPI)
or from source.

To install using :command:`pip`:


.. code-block:: console

    $ pip install -U Celery

.. _bundles:

Bundles
-------

Celery also defines a group of bundles that can be used
to install Celery and the dependencies for a given feature.

You can specify these in your requirements or on the :command:`pip`
command-line by using brackets. Multiple bundles can be specified by
separating them by commas.


.. code-block:: console

    $ pip install "celery[librabbitmq]"

    $ pip install "celery[librabbitmq,redis,auth,msgpack]"

The following bundles are available:

Serializers
~~~~~~~~~~~

:``celery[auth]``:
    for using the ``auth`` security serializer.

:``celery[msgpack]``:
    for using the msgpack serializer.

:``celery[yaml]``:
    for using the yaml serializer.

Concurrency
~~~~~~~~~~~

:``celery[eventlet]``:
    for using the :pypi:`eventlet` pool.

:``celery[gevent]``:
    for using the :pypi:`gevent` pool.

Transports and Backends
~~~~~~~~~~~~~~~~~~~~~~~

:``celery[librabbitmq]``:
    for using the librabbitmq C library.

:``celery[redis]``:
    for using Redis as a message transport or as a result backend.

:``celery[sqs]``:
    for using Amazon SQS as a message transport (*experimental*).

:``celery[tblib]``:
    for using the :setting:`task_remote_tracebacks` feature.

:``celery[memcache]``:
    for using Memcached as a result backend (using :pypi:`pylibmc`)

:``celery[pymemcache]``:
    for using Memcached as a result backend (pure-Python implementation).

:``celery[cassandra]``:
    for using Apache Cassandra/Astra DB as a result backend with DataStax driver.

:``celery[couchbase]``:
    for using Couchbase as a result backend.

:``celery[arangodb]``:
    for using ArangoDB as a result backend.

:``celery[elasticsearch]``:
    for using Elasticsearch as a result backend.

:``celery[riak]``:
    for using Riak as a result backend.

:``celery[dynamodb]``:
    for using AWS DynamoDB as a result backend.

:``celery[zookeeper]``:
    for using Zookeeper as a message transport.

:``celery[sqlalchemy]``:
    for using SQLAlchemy as a result backend (*supported*).

:``celery[pyro]``:
    for using the Pyro4 message transport (*experimental*).

:``celery[slmq]``:
    for using the SoftLayer Message Queue transport (*experimental*).

:``celery[consul]``:
    for using the Consul.io Key/Value store as a message transport or result backend (*experimental*).

:``celery[django]``:
    specifies the lowest version possible for Django support.

    You should probably not use this in your requirements, it's here
    for informational purposes only.

:``celery[gcs]``:
    for using the Google Cloud Storage as a result backend (*experimental*).

:``celery[gcpubsub]``:
    for using the Google Cloud Pub/Sub as a message transport (*experimental*)..



.. _celery-installing-from-source:

Downloading and installing from source
--------------------------------------

Download the latest version of Celery from PyPI:

https://pypi.org/project/celery/

You can install it by doing the following,:


.. code-block:: console

    $ tar xvfz celery-0.0.0.tar.gz
    $ cd celery-0.0.0
    $ python setup.py build
    # python setup.py install

The last command must be executed as a privileged user if
you aren't currently using a virtualenv.

.. _celery-installing-from-git:

Using the development version
-----------------------------

With pip
~~~~~~~~

The Celery development version also requires the development
versions of :pypi:`kombu`, :pypi:`amqp`, :pypi:`billiard`, and :pypi:`vine`.

You can install the latest snapshot of these using the following
pip commands:


.. code-block:: console

    $ pip install https://github.com/celery/celery/zipball/main#egg=celery
    $ pip install https://github.com/celery/billiard/zipball/main#egg=billiard
    $ pip install https://github.com/celery/py-amqp/zipball/main#egg=amqp
    $ pip install https://github.com/celery/kombu/zipball/main#egg=kombu
    $ pip install https://github.com/celery/vine/zipball/main#egg=vine

With git
~~~~~~~~

Please see the :ref:`Contributing <contributing>` section.
