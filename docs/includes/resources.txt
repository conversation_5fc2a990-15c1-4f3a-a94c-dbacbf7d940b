.. _getting-help:

Getting Help
============

.. warning::

    Our `Google Groups account <https://groups.google.com/group/celery-users/>`_ has been
    `compromised <https://github.com/celery/celery/discussions/9525>`_.

.. _social-media:

Social Media
============

Follow us on social media:

- `X <https://x.com/celeryorg>`_
- `LinkedIn <https://linkedin.com/company/celeryorg>`_

These accounts will (mostly) mirror each other, but we encourage you to
follow us on all platforms to ensure you don't miss any important updates.

.. _bug-tracker:

Bug tracker
===========

If you have any suggestions, bug reports, or annoyances please report them
to our issue tracker at https://github.com/celery/celery/issues/

.. _contributing-short:

Contributing
============

Development of `celery` happens at GitHub: https://github.com/celery/celery

You're highly encouraged to participate in the development
of `celery`. If you don't like GitHub (for some reason) you're welcome
to send regular patches.

Be sure to also read the `Contributing to Celery`_ section in the
documentation.

.. _`Contributing to Celery`:
    https://docs.celeryq.dev/en/main/contributing.html

.. _license:

License
=======

This software is licensed under the `New BSD License`. See the :file:`LICENSE`
file in the top distribution directory for the full license text.

.. # vim: syntax=rst expandtab tabstop=4 shiftwidth=4 shiftround
