=================================
 Celery - Distributed Task Queue
=================================

Celery is a simple, flexible, and reliable distributed system to
process vast amounts of messages, while providing operations with
the tools required to maintain such a system.

It's a task queue with focus on real-time processing, while also
supporting task scheduling.

Celery has a large and diverse community of users and contributors,
don't hesitate to ask questions or :ref:`get involved <getting-help>`.

Celery is Open Source and licensed under the `BSD License`_.

.. image:: https://opencollective.com/static/images/opencollectivelogo-footer-n.svg
   :target: https://opencollective.com/celery
   :alt: Open Collective logo
   :width: 240px

`Open Collective <https://opencollective.com/celery>`_ is our community-powered funding platform that fuels Celery's
ongoing development. Your sponsorship directly supports improvements, maintenance, and innovative features that keep
Celery robust and reliable.

Getting Started
===============

- If you're new to Celery you can get started by following
  the :ref:`first-steps` tutorial.

- You can also check out the :ref:`FAQ <faq>`.

.. _`BSD License`: http://www.opensource.org/licenses/BSD-3-Clause

Contents
========

.. toctree::
    :maxdepth: 1

    copyright

.. toctree::
    :maxdepth: 2

    getting-started/index
    userguide/index

.. toctree::
    :maxdepth: 1

    django/index
    contributing
    community
    tutorials/index
    faq
    changelog
    reference/index
    internals/index
    history/index
    glossary


Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
