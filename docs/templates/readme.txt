.. image:: https://docs.celeryq.dev/en/latest/_images/celery-banner-small.png

|build-status| |license| |wheel| |pyversion| |pyimp|

.. include:: ../includes/introduction.txt

.. include:: ../includes/installation.txt

.. include:: ../includes/resources.txt

.. |build-status| image:: https://secure.travis-ci.org/celery/celery.png?branch=main
    :alt: Build status
    :target: https://travis-ci.org/celery/celery

.. |coverage| image:: https://codecov.io/github/celery/celery/coverage.svg?branch=main
    :target: https://codecov.io/github/celery/celery?branch=main

.. |license| image:: https://img.shields.io/pypi/l/celery.svg
    :alt: BSD License
    :target: https://opensource.org/licenses/BSD-3-Clause

.. |wheel| image:: https://img.shields.io/pypi/wheel/celery.svg
    :alt: Celery can be installed via wheel
    :target: https://pypi.org/project/celery/

.. |pyversion| image:: https://img.shields.io/pypi/pyversions/celery.svg
    :alt: Supported Python versions.
    :target: https://pypi.org/project/celery/

.. |pyimp| image:: https://img.shields.io/pypi/implementation/celery.svg
    :alt: Support Python implementations.
    :target: https://pypi.org/project/celery/
