.. _apiref:

===============
 API Reference
===============

:Release: |version|
:Date: |today|

.. toctree::
    :maxdepth: 1

    cli
    celery
    celery.app
    celery.app.task
    celery.app.amqp
    celery.app.defaults
    celery.app.control
    celery.app.registry
    celery.app.backends
    celery.app.builtins
    celery.app.events
    celery.app.log
    celery.app.utils
    celery.app.autoretry
    celery.bootsteps
    celery.result
    celery.schedules
    celery.signals
    celery.security
    celery.utils.debug
    celery.exceptions
    celery.loaders
    celery.loaders.app
    celery.loaders.default
    celery.loaders.base
    celery.states
    celery.contrib.abortable
    celery.contrib.django.task
    celery.contrib.migrate
    celery.contrib.pytest
    celery.contrib.sphinx
    celery.contrib.testing.worker
    celery.contrib.testing.app
    celery.contrib.testing.manager
    celery.contrib.testing.mocks
    celery.contrib.rdb
    celery.events
    celery.events.receiver
    celery.events.dispatcher
    celery.events.event
    celery.events.state
    celery.beat
    celery.apps.worker
    celery.apps.beat
    celery.apps.multi
    celery.worker
    celery.worker.request
    celery.worker.state
    celery.worker.strategy
    celery.worker.consumer
    celery.worker.consumer.agent
    celery.worker.consumer.connection
    celery.worker.consumer.consumer
    celery.worker.consumer.control
    celery.worker.consumer.events
    celery.worker.consumer.gossip
    celery.worker.consumer.heart
    celery.worker.consumer.mingle
    celery.worker.consumer.tasks
    celery.worker.worker
    celery.bin.base
    celery.bin.celery
    celery.bin.worker
    celery.bin.beat
    celery.bin.events
    celery.bin.logtool
    celery.bin.amqp
    celery.bin.graph
    celery.bin.multi
    celery.bin.call
    celery.bin.control
    celery.bin.list
    celery.bin.migrate
    celery.bin.purge
    celery.bin.result
    celery.bin.shell
    celery.bin.upgrade
