.. _whatsnew-4.1:

===========================================
 What's new in Celery 4.1 (latentcall)
===========================================
:Author: <PERSON><PERSON> (``omer.drow at gmail.com``)

.. sidebar:: Change history

    What's new documents describe the changes in major versions,
    we also have a :ref:`changelog` that lists the changes in bugfix
    releases (0.0.x), while older series are archived under the :ref:`history`
    section.

Celery is a simple, flexible, and reliable distributed system to
process vast amounts of messages, while providing operations with
the tools required to maintain such a system.

It's a task queue with focus on real-time processing, while also
supporting task scheduling.

Celery has a large and diverse community of users and contributors,
you should come join us :ref:`on IRC <irc-channel>`
or :ref:`our mailing-list <mailing-list>`.

To read more about Celery you should go read the :ref:`introduction <intro>`.

While this version is backward compatible with previous versions
it's important that you read the following section.

This version is officially supported on CPython 2.7, 3.4, 3.5 & 3.6
and is also supported on PyPy.

.. _`website`: http://celeryproject.org/

.. topic:: Table of Contents

    Make sure you read the important notes before upgrading to this version.

.. contents::
    :local:
    :depth: 2

Preface
=======

The 4.1.0 release continues to improve our efforts to provide you with
the best task execution platform for Python.

This release is mainly a bug fix release, ironing out some issues and regressions
found in Celery 4.0.0.

We added official support for Python 3.6 and PyPy 5.8.0.

This is the first time we release without Ask Solem as an active contributor.
We'd like to thank him for his hard work in creating and maintaining Celery over the years.

Since Ask Solem was not involved there were a few kinks in the release process
which we promise to resolve in the next release.
This document was missing when we did release Celery 4.1.0.
Also, we did not update the release codename as we should have.
We apologize for the inconvenience.

For the time being, I, Omer Katz will be the release manager.

Thank you for your support!

*— Omer Katz*

Wall of Contributors
--------------------

Acey <<EMAIL>>
Acey9 <<EMAIL>>
Alan Hamlett <<EMAIL>>
Alan Justino da Silva <<EMAIL>>
Alejandro Pernin <<EMAIL>>
Alli <<EMAIL>>
Andreas Pelme <<EMAIL>>
Andrew de Quincey <<EMAIL>>
Anthony Lukach <<EMAIL>>
Arcadiy Ivanov <<EMAIL>>
Arnaud Rocher <<EMAIL>>
Arthur Vigil <<EMAIL>>
Asif Saifuddin Auvi <<EMAIL>>
Ask Solem <<EMAIL>>
BLAGA Razvan-Paul <<EMAIL>>
Brendan MacDonell <<EMAIL>>
Brian Luan <<EMAIL>>
Brian May <<EMAIL>>
Bruno Alla <<EMAIL>>
Chris Kuehl <<EMAIL>>
Christian <<EMAIL>>
Christopher Hoskin <<EMAIL>>
Daniel Hahler <<EMAIL>>
Daniel Huang <<EMAIL>>
Derek Harland <<EMAIL>>
Dmytro Petruk <<EMAIL>>
Ed Morley <<EMAIL>>
Eric Poelke <<EMAIL>>
Felipe <<EMAIL>>
François Voron <<EMAIL>>
GDR! <<EMAIL>>
George Psarakis <<EMAIL>>
J Alan Brogan <<EMAIL>>
James Michael DuPont <<EMAIL>>
Jamie Alessio <<EMAIL>>
Javier Domingo Cansino <<EMAIL>>
Jay McGrath <<EMAIL>>
Jian Yu <<EMAIL>>
Joey Wilhelm <<EMAIL>>
Jon Dufresne <<EMAIL>>
Kalle Bronsen <<EMAIL>>
Kirill Romanov <<EMAIL>>
Laurent Peuch <<EMAIL>>
Luke Plant <<EMAIL>>
Marat Sharafutdinov <<EMAIL>>
Marc Gibbons <<EMAIL>>
Marc Hörsken <<EMAIL>>
Michael <<EMAIL>>
Michael Howitz <<EMAIL>>
Michal Kuffa <<EMAIL>>
Mike Chen <<EMAIL>>
Mike Helmick <<EMAIL>>
Morgan Doocy <<EMAIL>>
Moussa Taifi <<EMAIL>>
Omer Katz <<EMAIL>>
Patrick Cloke <<EMAIL>>
Peter Bittner <<EMAIL>>
Preston Moore <<EMAIL>>
Primož Kerin <<EMAIL>>
Pysaoke <<EMAIL>>
Rick Wargo <<EMAIL>>
Rico Moorman <<EMAIL>>
Roman Sichny <<EMAIL>>
Ross Patterson <<EMAIL>>
Ryan Hiebert <<EMAIL>>
Rémi Marenco <<EMAIL>>
Salvatore Rinchiera <<EMAIL>>
Samuel Dion-Girardeau <<EMAIL>>
Sergey Fursov <<EMAIL>>
Simon Legner <<EMAIL>>
Simon Schmidt <<EMAIL>>
Slam <<EMAIL>>
Static <<EMAIL>>
Steffen Allner <<EMAIL>>
Steven <<EMAIL>>
Steven Johns <<EMAIL>>
Tamer Sherif <<EMAIL>>
Tao Qingyun <<EMAIL>>
Tayfun Sen <<EMAIL>>
Taylor C. Richberger <<EMAIL>>
Thierry RAMORASOAVINA <<EMAIL>>
Tom 'Biwaa' Riat <<EMAIL>>
Viktor Holmqvist <<EMAIL>>
Viraj <<EMAIL>>
Vivek Anand <<EMAIL>>
Will <<EMAIL>>
Wojciech Żywno <<EMAIL>>
Yoichi NAKAYAMA <<EMAIL>>
YuLun Shih <<EMAIL>>
Yuhannaa <<EMAIL>>
abhinav nilaratna <<EMAIL>>
aydin <<EMAIL>>
csfeathers <<EMAIL>>
georgepsarakis <<EMAIL>>
orf <<EMAIL>>
shalev67 <<EMAIL>>
sww <<EMAIL>>
tnir <<EMAIL>>
何翔宇(Sean Ho) <<EMAIL>>

.. note::

    This wall was automatically generated from git history,
    so sadly it doesn't not include the people who help with more important
    things like answering mailing-list questions.


.. _v410-important:

Important Notes
===============

Added support for Python 3.6 & PyPy 5.8.0
-----------------------------------------

We now run our unit test suite and integration test suite on Python 3.6.x
and PyPy 5.8.0.

We expect newer versions of PyPy to work but unfortunately we do not have the
resources to test PyPy with those versions.

The supported Python Versions are:

- CPython 2.7
- CPython 3.4
- CPython 3.5
- CPython 3.6
- PyPy 5.8 (``pypy2``)

.. _v410-news:

News
====

Result Backends
---------------

New DynamoDB Results Backend
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

We added a new results backend for those of you who are using DynamoDB.

If you are interested in using this results backend, refer to :ref:`conf-dynamodb-result-backend` for more information.

Elasticsearch
~~~~~~~~~~~~~

The Elasticsearch results backend is now more robust and configurable.

See :ref:`conf-elasticsearch-result-backend` for more information
about the new configuration options.

Redis
~~~~~

The Redis results backend can now use TLS to encrypt the communication with the
Redis database server.

See :ref:`conf-redis-result-backend`.

MongoDB
~~~~~~~

The MongoDB results backend can now handle binary-encoded task results.

This was a regression from 4.0.0 which resulted in a problem using serializers
such as MsgPack or Pickle in conjunction with the MongoDB results backend.

Periodic Tasks
--------------

The task schedule now updates automatically when new tasks are added.
Now if you use the Django database scheduler, you can add and remove tasks from the schedule without restarting Celery beat.

Tasks
-----

The ``disable_sync_subtasks`` argument was added to allow users to override disabling
synchronous subtasks.

See :ref:`task-synchronous-subtasks`

Canvas
------

Multiple bugs were resolved resulting in a much smoother experience when using Canvas.
