.. _whatsnew-4.2:

===========================================
 What's new in Celery 4.2 (windowlicker)
===========================================
:Author: <PERSON><PERSON> (``omer.drow at gmail.com``)

.. sidebar:: Change history

    What's new documents describe the changes in major versions,
    we also have a :ref:`changelog` that lists the changes in bugfix
    releases (0.0.x), while older series are archived under the :ref:`history`
    section.

Celery is a simple, flexible, and reliable distributed system to
process vast amounts of messages, while providing operations with
the tools required to maintain such a system.

It's a task queue with focus on real-time processing, while also
supporting task scheduling.

Celery has a large and diverse community of users and contributors,
you should come join us :ref:`on IRC <irc-channel>`
or :ref:`our mailing-list <mailing-list>`.

To read more about Celery you should go read the :ref:`introduction <intro>`.

While this version is backward compatible with previous versions
it's important that you read the following section.

This version is officially supported on CPython 2.7, 3.4, 3.5 & 3.6
and is also supported on PyPy.

.. _`website`: http://celeryproject.org/

.. topic:: Table of Contents

    Make sure you read the important notes before upgrading to this version.

.. contents::
    :local:
    :depth: 2

Preface
=======

The 4.2.0 release continues to improve our efforts to provide you with
the best task execution platform for Python.

This release is mainly a bug fix release, ironing out some issues and regressions
found in Celery 4.0.0.

Traditionally, releases were named after `Autechre <https://en.wikipedia.org/wiki/Autechre>`_'s track names.
This release continues this tradition in a slightly different way.
Each major version of Celery will use a different artist's track names as codenames.

From now on, the 4.x series will be codenamed after `Aphex Twin <https://en.wikipedia.org/wiki/Aphex_Twin>`_'s track names.
This release is codenamed after his very famous track, `Windowlicker <https://youtu.be/UBS4Gi1y_nc?t=4m>`_.

Thank you for your support!

*— Omer Katz*

Wall of Contributors
--------------------

Aaron Harnly <<EMAIL>>
Aaron Harnly <<EMAIL>>
Aaron McMillin <<EMAIL>>
Aaron Ross <<EMAIL>>
Aaron Ross <<EMAIL>>
Aaron Schumacher <<EMAIL>>
abecciu <<EMAIL>>
abhinav nilaratna <<EMAIL>>
Acey9 <<EMAIL>>
Acey <<EMAIL>>
aclowes <<EMAIL>>
Adam Chainz <<EMAIL>>
Adam DePue <<EMAIL>>
Adam Endicott <<EMAIL>>
Adam Renberg <<EMAIL>>
Adam Venturella <<EMAIL>>
Adaptification <<EMAIL>>
Adrian <<EMAIL>>
adriano petrich <<EMAIL>>
Adrian Rego <<EMAIL>>
Adrien Guinet <<EMAIL>>
Agris Ameriks <<EMAIL>>
Ahmet Demir <<EMAIL>>
air-upc <<EMAIL>>
Aitor Gómez-Goiri <<EMAIL>>
Akira Matsuzaki <<EMAIL>>
Akshar Raaj <<EMAIL>>
Alain Masiero <<EMAIL>>
Alan Hamlett <<EMAIL>>
Alan Hamlett <<EMAIL>>
Alan Justino <<EMAIL>>
Alan Justino da Silva <<EMAIL>>
Albert Wang <<EMAIL>>
Alcides Viamontes Esquivel <<EMAIL>>
Alec Clowes <<EMAIL>>
Alejandro Pernin <<EMAIL>>
Alejandro Varas <<EMAIL>>
Aleksandr Kuznetsov <<EMAIL>>
Ales Zoulek <<EMAIL>>
Alexander <<EMAIL>>
Alexander A. Sosnovskiy <<EMAIL>>
Alexander Koshelev <<EMAIL>>
Alexander Koval <<EMAIL>>
Alexander Oblovatniy <<EMAIL>>
Alexander Oblovatniy <<EMAIL>>
Alexander Ovechkin <<EMAIL>>
Alexander Smirnov <<EMAIL>>
Alexandru Chirila <<EMAIL>>
Alexey Kotlyarov <<EMAIL>>
Alexey Zatelepin <<EMAIL>>
Alex Garel <<EMAIL>>
Alex Hill <<EMAIL>>
Alex Kiriukha <<EMAIL>>
Alex Koshelev <<EMAIL>>
Alex Rattray <<EMAIL>>
Alex Williams <<EMAIL>>
Alex Zaitsev <<EMAIL>>
Ali Bozorgkhan <<EMAIL>>
Allan Caffee <<EMAIL>>
Allard Hoeve <<EMAIL>>
allenling <<EMAIL>>
Alli <<EMAIL>>
Alman One <<EMAIL>>
Alman One <<EMAIL>>
alman-one <<EMAIL>>
Amir Rustamzadeh <<EMAIL>>
<EMAIL> <<EMAIL>>
Anarchist666 <<EMAIL>>
Anders Pearson <<EMAIL>>
Andrea Rabbaglietti <<EMAIL>>
Andreas Pelme <<EMAIL>>
Andreas Savvides <<EMAIL>>
Andrei Fokau <<EMAIL>>
Andrew de Quincey <<EMAIL>>
Andrew Kittredge <<EMAIL>>
Andrew McFague <<EMAIL>>
Andrew Stewart <<EMAIL>>
Andrew Watts <<EMAIL>>
Andrew Wong <<EMAIL>>
Andrey Voronov <<EMAIL>>
Andriy Yurchuk <<EMAIL>>
Aneil Mallavarapu <<EMAIL>>
anentropic <<EMAIL>>
anh <<EMAIL>>
Ankur Dedania <<EMAIL>>
Anthony Lukach <<EMAIL>>
antlegrand <<EMAIL>>
Antoine Legrand <<EMAIL>>
Anton <<EMAIL>>
Anton Gladkov <<EMAIL>>
Antonin Delpeuch <<EMAIL>>
Arcadiy Ivanov <<EMAIL>>
areski <<EMAIL>>
Armenak Baburyan <<EMAIL>>
Armin Ronacher <<EMAIL>>
armo <<EMAIL>>
Arnaud Rocher <<EMAIL>>
arpanshah29 <<EMAIL>>
Arsenio Santos <<EMAIL>>
Arthur Vigil <<EMAIL>>
Arthur Vuillard <<EMAIL>>
Ashish Dubey <<EMAIL>>
Asif Saifuddin Auvi <<EMAIL>>
Asif Saifuddin Auvi <<EMAIL>>
ask <<EMAIL>>
Ask Solem <<EMAIL>>
Ask Solem <<EMAIL>>
Ask Solem Hoel <<EMAIL>>
aydin <<EMAIL>>
baeuml <<EMAIL>>
Balachandran C <<EMAIL>>
Balthazar Rouberol <<EMAIL>>
Balthazar Rouberol <<EMAIL>>
bartloop <<EMAIL>>
Bartosz Ptaszynski <>
Batiste Bieler <<EMAIL>>
bee-keeper <<EMAIL>>
Bence Tamas <<EMAIL>>
Ben Firshman <<EMAIL>>
Ben Welsh <<EMAIL>>
Berker Peksag <<EMAIL>>
Bert Vanderbauwhede <<EMAIL>>
Bert Vanderbauwhede <<EMAIL>>
BLAGA Razvan-Paul <<EMAIL>>
bobbybeever <<EMAIL>>
bobby <<EMAIL>>
Bobby Powers <<EMAIL>>
Bohdan Rybak <<EMAIL>>
Brad Jasper <<EMAIL>>
Branko Čibej <<EMAIL>>
BR <<EMAIL>>
Brendan MacDonell <<EMAIL>>
Brendon Crawford <<EMAIL>>
Brent Watson <<EMAIL>>
Brian Bouterse <<EMAIL>>
Brian Dixon <<EMAIL>>
Brian Luan <<EMAIL>>
Brian May <<EMAIL>>
Brian Peiris <<EMAIL>>
Brian Rosner <<EMAIL>>
Brodie Rao <<EMAIL>>
Bruno Alla <<EMAIL>>
Bryan Berg <<EMAIL>>
Bryan Berg <<EMAIL>>
Bryan Bishop <<EMAIL>>
Bryan Helmig <<EMAIL>>
Bryce Groff <<EMAIL>>
Caleb Mingle <<EMAIL>>
Carlos Garcia-Dubus <<EMAIL>>
Catalin Iacob <<EMAIL>>
Charles McLaughlin <<EMAIL>>
Chase Seibert <<EMAIL>>
ChillarAnand <<EMAIL>>
Chris Adams <<EMAIL>>
Chris Angove <<EMAIL>>
Chris Chamberlin <<EMAIL>>
chrisclark <<EMAIL>>
Chris Harris <<EMAIL>>
Chris Kuehl <<EMAIL>>
Chris Martin <<EMAIL>>
Chris Mitchell <<EMAIL>>
Chris Rose <<EMAIL>>
Chris St. Pierre <<EMAIL>>
Chris Streeter <<EMAIL>>
Christian <<EMAIL>>
Christoph Burgmer <<EMAIL>>
Christopher Hoskin <<EMAIL>>
Christopher Lee <<EMAIL>>
Christopher Peplin <<EMAIL>>
Christopher Peplin <<EMAIL>>
Christoph Krybus <<EMAIL>>
clayg <<EMAIL>>
Clay Gerrard <clayg@clayg-desktop.(none)>
Clemens Wolff <<EMAIL>>
cmclaughlin <<EMAIL>>
Codeb Fan <<EMAIL>>
Colin McIntosh <<EMAIL>>
Conrad Kramer <<EMAIL>>
Corey Farwell <<EMAIL>>
Craig Younkins <<EMAIL>>
csfeathers <<EMAIL>>
Cullen Rhodes <<EMAIL>>
daftshady <<EMAIL>>
Dan <<EMAIL>>
Dan Hackner <<EMAIL>>
Daniel Devine <<EMAIL>>
Daniele Procida <<EMAIL>>
Daniel Hahler <<EMAIL>>
Daniel Hepper <<EMAIL>>
Daniel Huang <<EMAIL>>
Daniel Lundin <<EMAIL>>
Daniel Lundin <<EMAIL>>
Daniel Watkins <<EMAIL>>
Danilo Bargen <<EMAIL>>
Dan McGee <<EMAIL>>
Dan McGee <<EMAIL>>
Dan Wilson <<EMAIL>>
Daodao <<EMAIL>>
Dave Smith <<EMAIL>>
Dave Smith <<EMAIL>>
David Arthur <<EMAIL>>
David Arthur <<EMAIL>>
David Baumgold <<EMAIL>>
David Cramer <<EMAIL>>
David Davis <<EMAIL>>
David Harrigan <<EMAIL>>
David Harrigan <<EMAIL>>
David Markey <<EMAIL>>
David Miller <<EMAIL>>
David Miller <<EMAIL>>
David Pravec <<EMAIL>>
David Pravec <<EMAIL>>
David Strauss <<EMAIL>>
David White <<EMAIL>>
DDevine <<EMAIL>>
Denis Podlesniy <<EMAIL>>
Denis Shirokov <<EMAIL>>
Dennis Brakhane <<EMAIL>>
Derek Harland <<EMAIL>>
derek_kim <<EMAIL>>
dessant <<EMAIL>>
Dieter Adriaenssens <<EMAIL>>
Dima Kurguzov <<EMAIL>>
dimka665 <<EMAIL>>
dimlev <<EMAIL>>
dmarkey <<EMAIL>>
Dmitry Malinovsky <<EMAIL>>
Dmitry Malinovsky <<EMAIL>>
dmollerm <<EMAIL>>
Dmytro Petruk <<EMAIL>>
dolugen <<EMAIL>>
dongweiming <<EMAIL>>
dongweiming <<EMAIL>>
Dongweiming <<EMAIL>>
dtheodor <<EMAIL>>
Dudás Ádám <<EMAIL>>
Dustin J. Mitchell <<EMAIL>>
D. Yu <<EMAIL>>
Ed Morley <<EMAIL>>
Eduardo Ramírez <<EMAIL>>
Edward Betts <<EMAIL>>
Emil Stanchev <<EMAIL>>
Eran Rundstein <eran@sandsquid.(none)>
ergo <<EMAIL>>
Eric Poelke <<EMAIL>>
Eric Zarowny <<EMAIL>>
ernop <<EMAIL>>
Evgeniy <<EMAIL>>
evildmp <<EMAIL>>
fatihsucu <<EMAIL>>
Fatih Sucu <<EMAIL>>
Feanil Patel <<EMAIL>>
Felipe <<EMAIL>>
Felipe Godói Rosário <<EMAIL>>
Felix Berger <<EMAIL>>
Fengyuan Chen <<EMAIL>>
Fernando Rocha <<EMAIL>>
ffeast <<EMAIL>>
Flavio Percoco Premoli <<EMAIL>>
Florian Apolloner <<EMAIL>>
Florian Apolloner <florian@apollo13.(none)>
Florian Demmer <<EMAIL>>
flyingfoxlee <<EMAIL>>
Francois Visconte <<EMAIL>>
François Voron <<EMAIL>>
Frédéric Junod <<EMAIL>>
fredj <<EMAIL>>
frol <<EMAIL>>
Gabriel <<EMAIL>>
Gao Jiangmiao <<EMAIL>>
GDR! <<EMAIL>>
GDvalle <<EMAIL>>
Geoffrey Bauduin <<EMAIL>>
georgepsarakis <<EMAIL>>
George Psarakis <<EMAIL>>
George Sibble <<EMAIL>>
George Tantiras <<EMAIL>>
Georgy Cheshkov <<EMAIL>>
Gerald Manipon <<EMAIL>>
German M. Bravo <<EMAIL>>
Gert Van Gool <<EMAIL>>
Gilles Dartiguelongue <<EMAIL>>
Gino Ledesma <<EMAIL>>
gmanipon <<EMAIL>>
Grant Thomas <<EMAIL>>
Greg Haskins <<EMAIL>>
gregoire <<EMAIL>>
Greg Taylor <<EMAIL>>
Greg Wilbur <<EMAIL>>
Guillaume Gauvrit <<EMAIL>>
Guillaume Gendre <<EMAIL>>
Gun.io Whitespace Robot <<EMAIL>>
Gunnlaugur Thor Briem <<EMAIL>>
harm <<EMAIL>>
Harm Verhagen <<EMAIL>>
Harry Moreno <<EMAIL>>
hclihn <<EMAIL>>
hekevintran <<EMAIL>>
honux <<EMAIL>>
Honza Kral <<EMAIL>>
Honza Král <<EMAIL>>
Hooksie <<EMAIL>>
Hsiaoming Yang <<EMAIL>>
Huang Huang <<EMAIL>>
Hynek Schlawack <<EMAIL>>
Hynek Schlawack <<EMAIL>>
Ian Dees <<EMAIL>>
Ian McCracken <<EMAIL>>
Ian Wilson <<EMAIL>>
Idan Kamara <<EMAIL>>
Ignas Mikalajūnas <<EMAIL>>
Igor Kasianov <<EMAIL>>
illes <<EMAIL>>
Ilya <<EMAIL>>
Ilya Georgievsky <<EMAIL>>
Ionel Cristian Mărieș <<EMAIL>>
Ionel Maries Cristian <<EMAIL>>
Ionut Turturica <<EMAIL>>
Iurii Kriachko <<EMAIL>>
Ivan Metzlar <<EMAIL>>
Ivan Virabyan <<EMAIL>>
j0hnsmith <<EMAIL>>
Jackie Leng <<EMAIL>>
J Alan Brogan <<EMAIL>>
Jameel Al-Aziz <<EMAIL>>
James M. Allen <<EMAIL>>
James Michael DuPont <<EMAIL>>
James Pulec <<EMAIL>>
James Remeika <<EMAIL>>
Jamie Alessio <<EMAIL>>
Jannis Leidel <<EMAIL>>
Jared Biel <<EMAIL>>
Jason Baker <<EMAIL>>
Jason Baker <<EMAIL>-domain>
Jason Veatch <<EMAIL>>
Jasper Bryant-Greene <<EMAIL>>
Javier Domingo Cansino <<EMAIL>>
Javier Martin Montull <<EMAIL>>
Jay Farrimond <<EMAIL>>
Jay McGrath <<EMAIL>>
jbiel <<EMAIL>>
jbochi <<EMAIL>>
Jed Smith <<EMAIL>>
Jeff Balogh <<EMAIL>>
Jeff Balogh <<EMAIL>>
Jeff Terrace <<EMAIL>>
Jeff Widman <<EMAIL>>
Jelle Verstraaten <<EMAIL>>
Jeremy Cline <<EMAIL>>
Jeremy Zafran <<EMAIL>>
jerry <<EMAIL>>
Jerzy Kozera <<EMAIL>>
Jerzy Kozera <<EMAIL>>
jespern <<EMAIL>>
Jesper Noehr <<EMAIL>>
Jesse <<EMAIL>>
jess <<EMAIL>>
Jess Johnson <<EMAIL>>
Jian Yu <<EMAIL>>
JJ <<EMAIL>>
João Ricardo <<EMAIL>>
Jocelyn Delalande <<EMAIL>>
JocelynDelalande <<EMAIL>>
Joe Jevnik <<EMAIL>>
Joe Sanford <<EMAIL>>
Joe Sanford <<EMAIL>>
Joey Wilhelm <<EMAIL>>
John Anderson <<EMAIL>>
John Arnold <<EMAIL>>
John Barham <<EMAIL>>
John Watson <<EMAIL>>
John Watson <<EMAIL>>
John Watson <<EMAIL>>
John Whitlock <<EMAIL>>
Jonas Haag <<EMAIL>>
Jonas Obrist <<EMAIL>>
Jonatan Heyman <<EMAIL>>
Jonathan Jordan <<EMAIL>>
Jonathan Sundqvist <<EMAIL>>
jonathan vanasco <<EMAIL>>
Jon Chen <<EMAIL>>
Jon Dufresne <<EMAIL>>
Josh <<EMAIL>>
Josh Kupershmidt <<EMAIL>>
Joshua "jag" Ginsberg <<EMAIL>>
Josue Balandrano Coronel <<EMAIL>>
Jozef <<EMAIL>>
jpellerin <jpellerin@jpdesk.(none)>
jpellerin <none@none>
JP <<EMAIL>>
JTill <<EMAIL>>
Juan Gutierrez <<EMAIL>>
Juan Ignacio Catalano <<EMAIL>>
Juan Rossi <<EMAIL>>
Juarez Bochi <<EMAIL>>
Jude Nagurney <<EMAIL>>
Julien Deniau <<EMAIL>>
julienp <<EMAIL>>
Julien Poissonnier <<EMAIL>>
Jun Sakai <<EMAIL>>
Justin Patrin <<EMAIL>>
Justin Patrin <<EMAIL>>
Kalle Bronsen <<EMAIL>>
kamalgill <<EMAIL>>
Kamil Breguła <<EMAIL>>
Kanan Rahimov <<EMAIL>>
Kareem Zidane <<EMAIL>>
Keith Perkins <<EMAIL>>
Ken Fromm <<EMAIL>>
Ken Reese <<EMAIL>>
keves <<EMAIL>>
Kevin Gu <<EMAIL>>
Kevin Harvey <<EMAIL>>
Kevin McCarthy <<EMAIL>>
Kevin Richardson <<EMAIL>>
Kevin Richardson <<EMAIL>>
Kevin Tran <<EMAIL>>
Kieran Brownlees <<EMAIL>>
Kirill Pavlov <<EMAIL>>
Kirill Romanov <<EMAIL>>
komu <<EMAIL>>
Konstantinos Koukopoulos <<EMAIL>>
Konstantin Podshumok <<EMAIL>>
Kornelijus Survila <<EMAIL>>
Kouhei Maeda <<EMAIL>>
Kracekumar Ramaraju <<EMAIL>>
Krzysztof Bujniewicz <<EMAIL>>
kuno <<EMAIL>>
Kxrr <<EMAIL>>
Kyle Kelley <<EMAIL>>
Laurent Peuch <<EMAIL>>
lead2gold <<EMAIL>>
Leo Dirac <<EMAIL>>
Leo Singer <<EMAIL>>
Lewis M. Kabui <<EMAIL>>
llllllllll <<EMAIL>>
Locker537 <<EMAIL>>
Loic Bistuer <<EMAIL>>
Loisaida Sam <<EMAIL>>
lookfwd <<EMAIL>>
Loren Abrams <<EMAIL>>
Loren Abrams <<EMAIL>>
Lucas Wiman <<EMAIL>>
lucio <<EMAIL>>
Luis Clara Gomez <<EMAIL>>
Lukas Linhart <<EMAIL>>
Łukasz Kożuchowski <<EMAIL>>
Łukasz Langa <<EMAIL>>
Łukasz Oleś <<EMAIL>>
Luke Burden <<EMAIL>>
Luke Hutscal <<EMAIL>>
Luke Plant <<EMAIL>>
Luke Pomfrey <<EMAIL>>
Luke Zapart <<EMAIL>>
mabouels <<EMAIL>>
Maciej Obuchowski <<EMAIL>>
Mads Jensen <<EMAIL>>
Manuel Kaufmann <<EMAIL>>
Manuel Vázquez Acosta <<EMAIL>>
Marat Sharafutdinov <<EMAIL>>
Marcelo Da Cruz Pinto <<EMAIL>>
Marc Gibbons <<EMAIL>>
Marc Hörsken <<EMAIL>>
Marcin Kuźmiński <<EMAIL>>
marcinkuzminski <<EMAIL>>
Marcio Ribeiro <<EMAIL>>
Marco Buttu <<EMAIL>>
Marco Schweighauser <<EMAIL>>
mariia-zelenova <<EMAIL>>
Marin Atanasov Nikolov <<EMAIL>>
Marius Gedminas <<EMAIL>>
mark hellewell <<EMAIL>>
Mark Lavin <<EMAIL>>
Mark Lavin <<EMAIL>>
Mark Parncutt <<EMAIL>>
Mark Story <<EMAIL>>
Mark Stover <<EMAIL>>
Mark Thurman <<EMAIL>>
Markus Kaiserswerth <<EMAIL>>
Markus Ullmann <<EMAIL>>
martialp <<EMAIL>>
Martin Davidsson <<EMAIL>>
Martin Galpin <<EMAIL>>
Martin Melin <*******************>
Matt Davis <<EMAIL>>
Matthew Duggan <<EMAIL>>
Matthew J Morrison <<EMAIL>>
Matthew Miller <<EMAIL>>
Matthew Schinckel <<EMAIL>>
mattlong <<EMAIL>>
Matt Long <<EMAIL>>
Matt Robenolt <<EMAIL>>
Matt Robenolt <<EMAIL>>
Matt Williamson <<EMAIL>>
Matt Williamson <<EMAIL>>
Matt Wise <<EMAIL>>
Matt Woodyard <<EMAIL>>
Mauro Rocco <<EMAIL>>
Maxim Bodyansky <maxim@viking.(none)>
Maxime Beauchemin <<EMAIL>>
Maxime Vdb <<EMAIL>>
Mayflower <<EMAIL>>
mbacho <<EMAIL>>
mher <<EMAIL>>
Mher Movsisyan <<EMAIL>>
Michael Aquilina <<EMAIL>>
Michael Duane Mooring <<EMAIL>>
<NAME_EMAIL> <michael@puppetmaster.(none)>
Michael Elsdorfer <<EMAIL>>
Michael Elsdörfer <<EMAIL>>
Michael Fladischer <<EMAIL>>
Michael Floering <<EMAIL>>
Michael Howitz <<EMAIL>>
michael <<EMAIL>>
Michael <<EMAIL>>
michael <michael@puppetmaster.(none)>
Michael Peake <<EMAIL>>
Michael Permana <<EMAIL>>
Michael Permana <<EMAIL>>
Michael Robellard <<EMAIL>>
Michael Robellard <<EMAIL>>
Michal Kuffa <<EMAIL>>
Miguel Hernandez Martos <<EMAIL>>
Mike Attwood <<EMAIL>>
Mike Chen <<EMAIL>>
Mike Helmick <<EMAIL>>
mikemccabe <<EMAIL>>
Mikhail Gusarov <<EMAIL>>
Mikhail Korobov <<EMAIL>>
Mikołaj <<EMAIL>>
Milen Pavlov <<EMAIL>>
Misha Wolfson <<EMAIL>>
Mitar <<EMAIL>>
Mitar <<EMAIL>>
Mitchel Humpherys <<EMAIL>>
mklauber <<EMAIL>>
mlissner <<EMAIL>>
monkut <<EMAIL>>
Morgan Doocy <<EMAIL>>
Morris Tweed <<EMAIL>>
Morton Fox <<EMAIL>>
Môshe van der Sterre <<EMAIL>>
Moussa Taifi <<EMAIL>>
mozillazg <<EMAIL>>
mpavlov <<EMAIL>>
mperice <<EMAIL>>
mrmmm <<EMAIL>>
Muneyuki Noguchi <<EMAIL>>
m-vdb <<EMAIL>>
nadad <<EMAIL>>
Nathaniel Varona <<EMAIL>>
Nathan Van Gheem <<EMAIL>>
Nat Williams <<EMAIL>>
Neil Chintomby <<EMAIL>>
Neil Chintomby <<EMAIL>>
Nicholas Pilon <<EMAIL>>
nicholsonjf <<EMAIL>>
Nick Eaket <<EMAIL>>
Nick Johnson <<EMAIL>>
Nicolas Mota <<EMAIL>>
nicolasunravel <<EMAIL>>
Niklas Aldergren <<EMAIL>>
Noah Kantrowitz <<EMAIL>>
Noel Remy <<EMAIL>>
NoKriK <<EMAIL>>
Norman Richards <<EMAIL>>
NotSqrt <<EMAIL>>
nott <<EMAIL>>
ocean1 <<EMAIL>>
ocean1 <<EMAIL>>
ocean1 <<EMAIL>>
OddBloke <<EMAIL>>
Oleg Anashkin <<EMAIL>>
Olivier Aubert <<EMAIL>>
Omar Khan <<EMAIL>>
Omer Katz <<EMAIL>>
Omer Korner <<EMAIL>>
orarbel <<EMAIL>>
orf <<EMAIL>>
Ori Hoch <<EMAIL>>
outself <<EMAIL>>
Pablo Marti <<EMAIL>>
pachewise <<EMAIL>>
partizan <<EMAIL>>
Pär Wieslander <<EMAIL>>
Patrick Altman <<EMAIL>>
Patrick Cloke <<EMAIL>>
Patrick <<EMAIL>>
Patrick Stegmann <<EMAIL>>
Patrick Stegmann <<EMAIL>>
Patrick Zhang <<EMAIL>>
Paul English <<EMAIL>>
Paul Jensen <<EMAIL>>
Paul Kilgo <<EMAIL>>
Paul McMillan <<EMAIL>>
Paul McMillan <<EMAIL>>
Paulo <<EMAIL>>
Paul Pearce <<EMAIL>>
Pavel Savchenko <<EMAIL>>
Pavlo Kapyshin <<EMAIL>>
pegler <<EMAIL>>
Pepijn de Vos <<EMAIL>>
Peter Bittner <<EMAIL>>
Peter Brook <<EMAIL>>
Philip Garnero <<EMAIL>>
Pierre Fersing <<EMAIL>>
Piotr Maślanka <<EMAIL>>
Piotr Sikora <<EMAIL>>
PMickael <<EMAIL>>
PMickael <<EMAIL>>
Polina Giralt <<EMAIL>>
precious <<EMAIL>>
Preston Moore <<EMAIL>>
Primož Kerin <<EMAIL>>
Pysaoke <<EMAIL>>
Rachel Johnson <<EMAIL>>
Rachel Willmer <<EMAIL>>
raducc <<EMAIL>>
Raf Geens <<EMAIL>>
Raghuram Srinivasan <<EMAIL>>
Raphaël Riel <<EMAIL>>
Raphaël Slinckx <<EMAIL>>
Régis B <<EMAIL>>
Remigiusz Modrzejewski <<EMAIL>>
Rémi Marenco <<EMAIL>>
rfkrocktk <<EMAIL>>
Rick van Hattem <<EMAIL>>
Rick Wargo <<EMAIL>>
Rico Moorman <<EMAIL>>
Rik <<EMAIL>>
Rinat Shigapov <<EMAIL>>
Riyad Parvez <<EMAIL>>
rlotun <<EMAIL>>
rnoel <<EMAIL>>
Robert Knight <<EMAIL>>
Roberto Gaiser <<EMAIL>>
roderick <<EMAIL>>
Rodolphe Quiedeville <<EMAIL>>
Roger Hu <<EMAIL>>
Roger Hu <<EMAIL>>
Roman Imankulov <<EMAIL>>
Roman Sichny <<EMAIL>>
Romuald Brunet <<EMAIL>>
Ronan Amicel <<EMAIL>>
Ross Deane <<EMAIL>>
Ross Lawley <<EMAIL>>
Ross Patterson <<EMAIL>>
Ross <<EMAIL>>
Rudy Attias <<EMAIL>>
rumyana neykova <<EMAIL>>
Rumyana Neykova <<EMAIL>>
Rune Halvorsen <<EMAIL>>
Rune Halvorsen <runeh@vorkosigan.(none)>
runeh <runeh@vorkosigan.(none)>
Russell Keith-Magee <<EMAIL>>
Ryan Guest <<EMAIL>>
Ryan Hiebert <<EMAIL>>
Ryan Kelly <<EMAIL>>
Ryan Luckie <<EMAIL>>
Ryan Petrello <<EMAIL>>
Ryan P. Kelly <<EMAIL>>
Ryan P Kilby <<EMAIL>>
Salvatore Rinchiera <<EMAIL>>
Sam Cooke <<EMAIL>>
samjy <<EMAIL>>
Sammie S. Taunton <<EMAIL>>
Samuel Dion-Girardeau <<EMAIL>>
Samuel Dion-Girardeau <<EMAIL>>
Samuel GIFFARD <<EMAIL>>
Scott Cooper <<EMAIL>>
screeley <screeley@screeley-laptop.(none)>
sdcooke <<EMAIL>>
Sean O'Connor <<EMAIL>>
Sean Wang <<EMAIL>>
Sebastian Kalinowski <<EMAIL>>
Sébastien Fievet <<EMAIL>>
Seong Won Mun <<EMAIL>>
Sergey Fursov <<EMAIL>>
Sergey Tikhonov <<EMAIL>>
Sergi Almacellas Abellana <<EMAIL>>
Sergio Fernandez <<EMAIL>>
Seungha Kim <<EMAIL>>
shalev67 <<EMAIL>>
Shitikanth <<EMAIL>>
Silas Sewell <<EMAIL>>
Simon Charette <<EMAIL>>
Simon Engledew <<EMAIL>>
Simon Josi <<EMAIL>>
Simon Legner <<EMAIL>>
Simon Peeters <<EMAIL>>
Simon Schmidt <<EMAIL>>
skovorodkin <<EMAIL>>
Slam <<EMAIL>>
Smirl <<EMAIL>>
squfrans <<EMAIL>>
Srinivas Garlapati <<EMAIL>>
Stas Rudakou <<EMAIL>>
Static <<EMAIL>>
Steeve Morin <<EMAIL>>
Stefan hr Berder <<EMAIL>>
Stefan Kjartansson <<EMAIL>>
Steffen Allner <<EMAIL>>
Stephen Weber <<EMAIL>>
Steven Johns <<EMAIL>>
Steven Parker <<EMAIL>>
Steven <<EMAIL>>
Steven Sklar <<EMAIL>>
Steven Skoczen <<EMAIL>>
Steven Skoczen <<EMAIL>>
Steve Peak <<EMAIL>>
stipa <<EMAIL>>
sukrit007 <<EMAIL>>
Sukrit Khera <<EMAIL>>
Sundar Raman <<EMAIL>>
sunfinite <<EMAIL>>
sww <<EMAIL>>
Tadej Janež <<EMAIL>>
Taha Jahangir <<EMAIL>>
Takeshi Kanemoto <<EMAIL>>
TakesxiSximada <<EMAIL>>
Tamer Sherif <<EMAIL>>
Tao Qingyun <<EMAIL>>
Tarun Bhardwaj <<EMAIL>>
Tayfun Sen <<EMAIL>>
Tayfun Sen <<EMAIL>>
Tayfun Sen <<EMAIL>>
tayfun <<EMAIL>>
Taylor C. Richberger <<EMAIL>>
taylornelson <<EMAIL>>
Theodore Dubois <<EMAIL>>
Theo Spears <<EMAIL>>
Thierry RAMORASOAVINA <<EMAIL>>
Thijs Triemstra <<EMAIL>>
Thomas French <<EMAIL>>
Thomas Grainger <<EMAIL>>
Thomas Johansson <<EMAIL>>
Thomas Meson <<EMAIL>>
Thomas Minor <<EMAIL>>
Thomas Wright <<EMAIL>>
Timo Sugliani <<EMAIL>>
Timo Sugliani <tsugliani@tsugliani-desktop.(none)>
Titusz <<EMAIL>>
tnir <<EMAIL>>
Tobias Kunze <<EMAIL>>
Tocho Tochev <<EMAIL>>
Tomas Machalek <<EMAIL>>
Tomasz Święcicki <<EMAIL>>
Tom 'Biwaa' Riat <<EMAIL>>
Tomek Święcicki <<EMAIL>>
Tom S <<EMAIL>>
tothegump <<EMAIL>>
Travis Swicegood <<EMAIL>>
Travis Swicegood <<EMAIL>>
Travis <<EMAIL>>
Trevor Skaggs <<EMAIL>>
Ujjwal Ojha <<EMAIL>>
unknown <Jonatan@.(none)>
Valentyn Klindukh <<EMAIL>>
Viktor Holmqvist <<EMAIL>>
Vincent Barbaresi <<EMAIL>>
Vincent Driessen <<EMAIL>>
Vinod Chandru <<EMAIL>>
Viraj <<EMAIL>>
Vitaly Babiy <<EMAIL>>
Vitaly <<EMAIL>>
Vivek Anand <<EMAIL>>
Vlad <<EMAIL>>
Vladimir Gorbunov <<EMAIL>>
Vladimir Kryachko <<EMAIL>>
Vladimir Rutsky <<EMAIL>>
Vladislav Stepanov <<EMAIL>>
Vsevolod <<EMAIL>>
Wes Turner <<EMAIL>>
wes <<EMAIL>>
Wes Winham <<EMAIL>>
w- <<EMAIL>>
whendrik <<EMAIL>>
Wido den Hollander <<EMAIL>>
Wieland Hoffmann <<EMAIL>>
Wiliam Souza <<EMAIL>>
Wil Langford <<EMAIL>>
William King <<EMAIL>>
Will <<EMAIL>>
Will Thompson <<EMAIL>>
winhamwr <<EMAIL>>
Wojciech Żywno <<EMAIL>>
W. Trevor King <<EMAIL>>
wyc <<EMAIL>>
wyc <<EMAIL>>
xando <<EMAIL>>
Xavier Damman <<EMAIL>>
Xavier Hardy <<EMAIL>>
Xavier Ordoquy <<EMAIL>>
xin li <<EMAIL>>
xray7224 <<EMAIL>>
y0ngdi <<EMAIL>>
Yan Kalchevskiy <<EMAIL>>
Yohann Rebattu <<EMAIL>>
Yoichi NAKAYAMA <<EMAIL>>
Yuhannaa <<EMAIL>>
YuLun Shih <<EMAIL>>
Yury V. Zaytsev <<EMAIL>>
Yuval Greenfield <<EMAIL>>
Zach Smith <<EMAIL>>
Zhang Chi <<EMAIL>>
Zhaorong Ma <<EMAIL>>
Zoran Pavlovic <<EMAIL>>
ztlpn <<EMAIL>>
何翔宇(Sean Ho) <<EMAIL>>
許邱翔 <<EMAIL>>

.. note::

    This wall was automatically generated from git history,
    so sadly it doesn't not include the people who help with more important
    things like answering mailing-list questions.


.. _v420-important:

Important Notes
===============

Supported Python Versions
-------------------------

The supported Python Versions are:

- CPython 2.7
- CPython 3.4
- CPython 3.5
- CPython 3.6
- PyPy 5.8 (``pypy2``)

.. _v420-news:

News
====

Result Backends
---------------

New Redis Sentinel Results Backend
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Redis Sentinel provides high availability for Redis.
A new result backend supporting it was added.

Cassandra Results Backend
~~~~~~~~~~~~~~~~~~~~~~~~~

A new `cassandra_options` configuration option was introduced in order to configure
the cassandra client.

See :ref:`conf-cassandra-result-backend` for more information.

DynamoDB Results Backend
~~~~~~~~~~~~~~~~~~~~~~~~

A new `dynamodb_endpoint_url` configuration option was introduced in order
to point the result backend to a local endpoint during development or testing.

See :ref:`conf-dynamodb-result-backend` for more information.

Python 2/3 Compatibility Fixes
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Both the CouchDB and the Consul result backends accepted byte strings without decoding them to Unicode first.
This is now no longer the case.

Canvas
------

Multiple bugs were resolved resulting in a much smoother experience when using Canvas.

Tasks
-----

Bound Tasks as Error Callbacks
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

We fixed a regression that occurred when bound tasks are used as error callbacks.
This used to work in Celery 3.x but raised an exception in 4.x until this release.

In both 4.0 and 4.1 the following code wouldn't work:

.. code-block:: python

  @app.task(name="raise_exception", bind=True)
  def raise_exception(self):
      raise Exception("Bad things happened")


  @app.task(name="handle_task_exception", bind=True)
  def handle_task_exception(self):
      print("Exception detected")

  subtask = raise_exception.subtask()

  subtask.apply_async(link_error=handle_task_exception.s())

Task Representation
~~~~~~~~~~~~~~~~~~~

- Shadowing task names now works as expected.
  The shadowed name is properly presented in flower, the logs and the traces.
- `argsrepr` and `kwargsrepr` were previously not used even if specified.
  They now work as expected. See :ref:`task-hiding-sensitive-information` for more information.

Custom Requests
~~~~~~~~~~~~~~~

We now allow tasks to use custom `request <celery.worker.request.Request>`:class: classes
for custom task classes.

See :ref:`task-requests-and-custom-requests` for more information.

Retries with Exponential Backoff
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Retries can now be performed with exponential backoffs to avoid overwhelming
external services with requests.

See :ref:`task-autoretry` for more information.

Sphinx Extension
----------------

Tasks were supposed to be automatically documented when using Sphinx's Autodoc was used.
The code that would have allowed automatic documentation had a few bugs which are now fixed.

Also, The extension is now documented properly. See :ref:`sphinx` for more information.
