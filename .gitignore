.DS_Store
*.pyc
*$py.class
*~
.*.sw[pon]
dist/
*.egg-info
*.egg
*.egg/
*.eggs/
build/
.build/
_build/
pip-log.txt
.directory
erl_crash.dump
*.db
Documentation/
.tox/
.ropeproject/
.project
.pydevproject
.idea/
.coverage
celery/tests/cover/
.ve*
cover/
.vagrant/
.cache/
htmlcov/
coverage.xml
test.db
pip-wheel-metadata/
.python-version
.vscode/
integration-tests-config.json
[0-9]*
statefilename.*
dump.rdb
.env
junit.xml
