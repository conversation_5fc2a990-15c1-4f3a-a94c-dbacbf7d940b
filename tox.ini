[tox]
requires =
    tox-gh-actions
envlist =
    {3.8,3.9,3.10,3.11,3.12,3.13,pypy3}-unit
    {3.8,3.9,3.10,3.11,3.12,3.13,pypy3}-integration-{rabbitmq_redis,rabbitmq,redis,dynamodb,azureblockblob,cache,cassandra,elasticsearch,docker}
    {3.8,3.9,3.10,3.11,3.12,3.13,pypy3}-smoke

    flake8
    apicheck
    configcheck
    bandit


[gh-actions]
python =
    3.8: 3.8-unit
    3.9: 3.9-unit
    3.10: 3.10-unit
    3.11: 3.11-unit
    3.12: 3.12-unit
    3.13: 3.13-unit
    pypy-3: pypy3-unit

[testenv]
sitepackages = False
recreate = False
passenv =
    AZUREBLOCKBLOB_URL

deps=
    -r{toxinidir}/requirements/test.txt
    -r{toxinidir}/requirements/pkgutils.txt

    3.8,3.9,3.10,3.11,3.12,3.13: -r{toxinidir}/requirements/test-ci-default.txt
    3.8,3.9,3.10,3.11,3.12,3.13: -r{toxinidir}/requirements/docs.txt
    pypy3: -r{toxinidir}/requirements/test-ci-default.txt

    integration: -r{toxinidir}/requirements/test-integration.txt
    smoke: pytest-xdist>=3.5

    linkcheck,apicheck,configcheck: -r{toxinidir}/requirements/docs.txt
    lint: pre-commit
    bandit: bandit

commands =
    unit: pytest -vv --maxfail=10 --capture=no -v --cov=celery --cov-report=xml --junitxml=junit.xml -o junit_family=legacy --cov-report term {posargs}
    integration: pytest -xsvv t/integration {posargs}
    smoke: pytest -xsvv t/smoke --dist=loadscope --reruns 5 --reruns-delay 10 {posargs}
setenv =
    PIP_EXTRA_INDEX_URL=https://celery.github.io/celery-wheelhouse/repo/simple/
    BOTO_CONFIG = /dev/null
    WORKER_LOGLEVEL = INFO
    PYTHONIOENCODING = UTF-8
    PYTHONUNBUFFERED = 1
    PYTHONDONTWRITEBYTECODE = 1

    cache: TEST_BROKER=redis://
    cache: TEST_BACKEND=cache+pylibmc://

    cassandra: TEST_BROKER=redis://
    cassandra: TEST_BACKEND=cassandra://

    elasticsearch: TEST_BROKER=redis://
    elasticsearch: TEST_BACKEND=elasticsearch://@localhost:9200

    rabbitmq: TEST_BROKER=pyamqp://
    rabbitmq: TEST_BACKEND=rpc

    redis: TEST_BROKER=redis://
    redis: TEST_BACKEND=redis://

    rabbitmq_redis: TEST_BROKER=pyamqp://
    rabbitmq_redis: TEST_BACKEND=redis://

    docker: TEST_BROKER=pyamqp://rabbit:5672
    docker: TEST_BACKEND=redis://redis

    dynamodb: TEST_BROKER=redis://
    dynamodb: TEST_BACKEND=dynamodb://@localhost:8000
    dynamodb: AWS_ACCESS_KEY_ID=test_aws_key_id
    dynamodb: AWS_SECRET_ACCESS_KEY=test_aws_secret_key

    azureblockblob: TEST_BROKER=redis://
    azureblockblob: TEST_BACKEND=azureblockblob://DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;

basepython =
    3.8: python3.8
    3.9: python3.9
    3.10: python3.10
    3.11: python3.11
    3.12: python3.12
    3.13: python3.13
    pypy3: pypy3
    mypy: python3.13
    lint,apicheck,linkcheck,configcheck,bandit: python3.13
usedevelop = True

[testenv:mypy]
commands = python -m mypy --config-file pyproject.toml

[testenv:apicheck]
setenv =
    PYTHONHASHSEED = 100
commands =
    sphinx-build -j2 -b apicheck -d {envtmpdir}/doctrees docs docs/_build/apicheck

[testenv:configcheck]
commands =
    sphinx-build -j2 -b configcheck -d {envtmpdir}/doctrees docs docs/_build/configcheck

[testenv:linkcheck]
commands =
    sphinx-build -j2 -b linkcheck -d {envtmpdir}/doctrees docs docs/_build/linkcheck

[testenv:bandit]
commands =
    bandit -b bandit.json -r celery/

[testenv:lint]
commands =
    pre-commit {posargs:run --all-files --show-diff-on-failure}

[testenv:clean]
deps = cleanpy
allowlist_externals = bash, make, rm
commands =
    bash -c 'files=$(find . -name "*.coverage*" -type f); if [ -n "$files" ]; then echo "Removed coverage file(s):"; echo "$files" | tr " " "\n"; rm $files; fi'
    bash -c 'containers=$(docker ps -aq --filter label=creator=pytest-docker-tools); if [ -n "$containers" ]; then echo "Removed Docker container(s):"; docker rm -f $containers; fi'
    bash -c 'networks=$(docker network ls --filter name=pytest- -q); if [ -n "$networks" ]; then echo "Removed Docker network(s):"; docker network rm $networks; fi'
    bash -c 'volumes=$(docker volume ls --filter name=pytest- -q); if [ -n "$volumes" ]; then echo "Removed Docker volume(s):"; docker volume rm $volumes; fi'
    python -m cleanpy .
    make clean
    rm -f test.db statefilename.db 86
